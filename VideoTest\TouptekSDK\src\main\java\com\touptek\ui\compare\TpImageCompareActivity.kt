package com.touptek.ui.compare

import android.content.Context
import android.content.Intent
import android.graphics.Matrix
import android.os.Bundle
import android.util.Log
import android.view.WindowManager
import android.widget.ImageButton
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.touptek.ui.TpImageView
import com.touptek.video.TpVideoSystem
import com.touptek.video.TpVideoConfig
import android.graphics.BitmapFactory
import java.io.File
import com.touptek.R

/**
 * 双图对比Activity
 * 
 * 支持两张图片的并排对比，包括同步缩放、平移等功能
 */
class TpImageCompareActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "TpImageCompareActivity"
        private const val EXTRA_IMAGE_PATHS = "extra_image_paths"

        /**
         * 启动双图对比Activity
         */
        @JvmStatic
        fun start(context: Context, imagePaths: List<String>) {
            if (imagePaths.size != 2) {
                Toast.makeText(context, "需要提供2张图片路径", Toast.LENGTH_SHORT).show()
                return
            }
            val intent = Intent(context, TpImageCompareActivity::class.java).apply {
                putStringArrayListExtra(EXTRA_IMAGE_PATHS, ArrayList(imagePaths))
            }
            context.startActivity(intent)
        }
    }
    
    // UI组件
    private lateinit var leftImageView: TpImageView
    private lateinit var rightImageView: TpImageView
    private lateinit var btnBack: ImageButton
    private lateinit var btnSync: ImageButton
    private lateinit var btnReset: ImageButton
    private lateinit var btnSwap: ImageButton
    private lateinit var tvLeftInfo: TextView
    private lateinit var tvRightInfo: TextView
    
    // 数据
    private var imagePaths = mutableListOf<String>()

    // 同步引擎
    private lateinit var syncEngine: TpImageSyncEngine
    private var isSyncEnabled = true
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupFullScreen()
        setContentView(R.layout.tp_image_compare_dual)

        initViews()
        getIntentData()
        setupSyncEngine()
        loadImages()
        updateImageInfo()
        setupClickListeners()
    }

    private fun setupFullScreen() {
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
    }
    
    private fun initViews() {
        leftImageView = findViewById(R.id.left_image)
        rightImageView = findViewById(R.id.right_image)
        btnBack = findViewById(R.id.btn_back)
        btnSync = findViewById(R.id.btn_sync)
        btnReset = findViewById(R.id.btn_reset)
        btnSwap = findViewById(R.id.btn_swap)
        tvLeftInfo = findViewById(R.id.tv_left_info)
        tvRightInfo = findViewById(R.id.tv_right_info)
    }
    
    private fun getIntentData() {
        imagePaths = intent.getStringArrayListExtra(EXTRA_IMAGE_PATHS)?.toMutableList() ?: mutableListOf()

        if (imagePaths.size != 2) {
            Toast.makeText(this, "图片路径无效", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        Log.d(TAG, "接收到${imagePaths.size}张图片路径")
    }
    
    private fun setupSyncEngine() {
        syncEngine = TpImageSyncEngine.create().apply {
            setSyncMode(TpImageSyncEngine.SyncMode.GLOBAL)
            setSyncEnabled(isSyncEnabled)
            addImageView("left", leftImageView)
            addImageView("right", rightImageView)
        }
    }
    
    private fun loadImages() {
        val videoConfig = TpVideoConfig.createDefault4K()
        val videoSystem = TpVideoSystem(this, videoConfig)
        
        try {
            videoSystem.loadFullImage(imagePaths[0], leftImageView)
            videoSystem.loadFullImage(imagePaths[1], rightImageView)
            Log.d(TAG, "图片加载成功")
        } catch (e: Exception) {
            Log.e(TAG, "图片加载失败", e)
            Toast.makeText(this, "图片加载失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun updateImageInfo() {
        imagePaths.forEachIndexed { index, path ->
            val fileName = File(path).name
            val resolution = getImageResolution(path)
            val info = "$fileName ($resolution)"

            when (index) {
                0 -> tvLeftInfo.text = info
                1 -> tvRightInfo.text = info
            }
        }
    }
    
    private fun getImageResolution(imagePath: String): String {
        return try {
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(imagePath, options)
            val width = options.outWidth
            val height = options.outHeight
            if (width > 0 && height > 0) {
                "${width}×${height}"
            } else {
                "未知分辨率"
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取图片分辨率失败: $imagePath", e)
            "未知分辨率"
        }
    }
    
    private fun setupClickListeners() {
        btnBack.setOnClickListener { finish() }
        
        btnSync.setOnClickListener {
            isSyncEnabled = !isSyncEnabled
            syncEngine.setSyncEnabled(isSyncEnabled)
            updateSyncButtonState()
            
            val message = if (isSyncEnabled) "已开启同步缩放" else "已关闭同步缩放"
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
        }
        
        btnReset.setOnClickListener {
            loadImages()
            Toast.makeText(this, "已重置图片位置", Toast.LENGTH_SHORT).show()
        }
        
        btnSwap.setOnClickListener {
            swapImages()
        }
        
        updateSyncButtonState()
    }
    
    private fun updateSyncButtonState() {
        btnSync.alpha = if (isSyncEnabled) 1.0f else 0.5f
    }
    
    private fun swapImages() {
        // 交换图片路径
        val tempPath = imagePaths[0]
        imagePaths[0] = imagePaths[1]
        imagePaths[1] = tempPath
        
        // 重新加载图片
        loadImages()
        updateImageInfo()
        
        Toast.makeText(this, "已交换图片位置", Toast.LENGTH_SHORT).show()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 清理同步引擎资源
        leftImageView.setMatrixChangeListener(null)
        rightImageView.setMatrixChangeListener(null)
    }
}
