package com.touptek.xcamview.activity.browse

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.touptek.xcamview.R
import com.touptek.video.internal.TpImageLoader
import java.io.File
import java.util.Locale
import kotlin.collections.forEach
import kotlin.collections.getOrElse
import kotlin.collections.isNotEmpty
import kotlin.collections.toList
import kotlin.collections.toMutableList
import kotlin.io.extension
import kotlin.let
import kotlin.text.contains
import kotlin.text.toLowerCase

class TpOperationDirAdapter(
    private var imageFiles: List<File>,  // 改为可变文件列表
    private var labels: List<String>,    // 改为可变标签列表
    private val onClick: (Int) -> Unit,
    private val onDoubleClick: (Int) -> Unit,
    private val onUpdate: () -> Unit,
) : RecyclerView.Adapter<TpOperationDirAdapter.ViewHolder>() {

    private val selectedItems = mutableSetOf<Int>()
    var allowSelectionMode = true

    // 新增部分开始
    var isSelectionMode = false
    var onSelectionChanged: ((Int) -> Unit)? = null // 选中项变化回调
    // 新增部分结束

    inner class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val imageView: ImageView = view.findViewById(R.id.grid_image)
        val labelView: TextView = view.findViewById(R.id.grid_label)
        val selectedOverlay: View = view.findViewById(R.id.selected_overlay)
        val videoIndicator: ImageView = view.findViewById(R.id.video_indicator)

        val checkBox: ImageView = view.findViewById(R.id.grid_checkbox) // 勾选框

        var lastClickTime: Long = 0
        var pendingClickRunnable: Runnable? = null

    }

    fun updateData(newFiles: List<File>, newLabels: List<String>) {
        imageFiles = newFiles.toMutableList() //toMutableList将list改为一个可变集合
        imageFiles.forEach { file ->
        }

        labels = newLabels.toMutableList()

        if(imageFiles.isNotEmpty()) {
            notifyDataSetChanged()
            val previousSelected = selectedItems.toList()
            selectedItems.clear()
            previousSelected.forEach { notifyItemChanged(it) }
        }
        else
        {

            selectedItems.clear()  //切换文件夹时取消所有文件的选中
            clearData()
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.operation_grid_layout, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        //文件夹
        val item = imageFiles[position]

        // 如果是文件夹，显示iOS风格文件夹图标
        if (item.isDirectory) {

            holder.imageView.setImageResource(R.drawable.ic_folder_ios)
            holder.videoIndicator.visibility = View.GONE

            // 特殊处理".." (上级目录)
            if (item.name == item.parent) {
                holder.labelView.text = ".."
            }
        }
        // 如果是文件，正常加载缩略图
        else {
            // 使用TpImageLoader加载缩略图（用于列表显示）
            TpImageLoader.loadThumbnail(item.absolutePath, holder.imageView)

            // 添加视频标记
            val isVideo = item.name.contains(".mp4", ignoreCase = true) ||
                    getFileType(item) == "MP4"
            holder.videoIndicator.visibility = if (isVideo) View.VISIBLE else View.GONE
        }

        // 标签显示处理
        holder.labelView.text = labels.getOrElse(position) { "" }

        // 设置勾选框可见性和选中状态
        holder.checkBox.visibility = if (isSelectionMode) View.VISIBLE else View.INVISIBLE
        holder.checkBox.setImageResource(
            if (selectedItems.contains(position)) R.drawable.ic_checked
            else R.drawable.ic_unchecked
        )

        // 单击事件处理
        holder.itemView.setOnClickListener {
            if (isSelectionMode) {
                toggleSelection(position)
            } else {
                holder.pendingClickRunnable?.let { runnable ->
                    holder.itemView.removeCallbacks(runnable)
                    holder.pendingClickRunnable = null
                }

                val currentTime = System.currentTimeMillis()
                if (currentTime - holder.lastClickTime < 300) {
                    holder.lastClickTime = 0
                    onDoubleClick(position)
                } else {
                    holder.lastClickTime = currentTime
                    holder.pendingClickRunnable = Runnable {
                        onClick(position)
                        holder.pendingClickRunnable = null
                    }
                    holder.itemView.postDelayed(holder.pendingClickRunnable!!, 300)
                }
            }
        }

        holder.checkBox.visibility = if (allowSelectionMode && isSelectionMode) {
            View.VISIBLE
        } else {
            View.GONE
        }
    }

    // 新增选择模式相关方法
    fun enterSelectionMode() {
        isSelectionMode = true
        notifyDataSetChanged() // 刷新所有视图显示勾选框
        onUpdate()
    }

    fun exitSelectionMode() {
        isSelectionMode = false
        selectedItems.clear()
        onSelectionChanged?.invoke(0) // 通知选中项清零
        notifyDataSetChanged() // 刷新所有视图隐藏勾选框
        onUpdate()
    }

    private fun toggleSelection(position: Int) {
        if (selectedItems.contains(position)) {
            selectedItems.remove(position)
        } else {
            selectedItems.add(position)
        }

        // 通知选中项数量变化
        onSelectionChanged?.invoke(selectedItems.size)

        notifyItemChanged(position)
    }

    override fun getItemCount(): Int {
        return imageFiles.size
    }

    fun isPositionSelected(position: Int): Boolean {
        return selectedItems.contains(position)
    }

    fun clearData() {
        imageFiles = emptyList()
        labels = emptyList()
        selectedItems.clear()
        // 直接通知整个数据集变化
        notifyDataSetChanged()
    }

    private fun getFileType(file: File): String {
        return when(file.extension.toLowerCase(Locale.US)) {
            "mp4", "mov", "avi" -> "MP4"
            "png" -> "PNG image"
            "jpg", "jpeg" -> "JPEG image"
            else -> "File"
        }
    }


    // 添加公共访问方法
    fun getFiles(): List<File> {
        return imageFiles
    }

    // 或者添加通过位置获取文件的方法
    fun getFileAt(position: Int): File {
        return imageFiles[position]
    }
}