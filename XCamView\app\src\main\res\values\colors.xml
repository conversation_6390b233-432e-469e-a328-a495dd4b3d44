<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- 现代化配色方案 - iOS/现代风格 -->
    <!-- 主色调 - 现代蓝色系 -->
    <color name="primary">#007AFF</color>
    <color name="primary_dark">#0056CC</color>
    <color name="accent">#FF3B30</color>

    <!-- 中性色系 -->
    <color name="background">#F2F2F7</color>
    <color name="surface">#FFFFFF</color>
    <color name="surface_secondary">#F8F8F8</color>
    <color name="divider">#E5E5EA</color>

    <!-- 文本色系 -->
    <color name="text_primary">#000000</color>
    <color name="text_secondary">#8E8E93</color>
    <color name="text_tertiary">#C7C7CC</color>

    <!-- 选中状态 -->
    <color name="selection_overlay">#4D007AFF</color>
    <color name="selection_border">#007AFF</color>

    <!-- 兼容旧配色 -->
    <color name="primary_modern">#007AFF</color>
    <color name="on_surface_modern">#000000</color>

    <!-- 添加以下缺失的颜色资源定义 -->
    <color name="black">#FF000000</color>
    <color name="white_background">#FFFFFF</color>
    <color name="grey_background">#AAAAAA</color>
    <color name="border_color">#999999</color>
    <color name="solid_blue">#0000FF</color>
    <color name="Light_black">#FF2E2E2E</color>
    <color name="popup_text_color">#FFFFFF</color>
    <color name="popup_background_color">#555555</color>
    <color name="gray_text_disabled">#A0A0A0</color>
    <color name="gray_icon_disabled">#B0B0B0</color>

    <color name="colorPrimary">#FFA0A0A0</color> <!-- 边框颜色 -->
    <color name="colorISPText">#333333</color>
    <color name="colorISPBlue">#FF2196F3</color>


    <!-- Switch -->
    <color name="blue_500">#FF2196F3</color> <!-- 主蓝色 -->
    <color name="gray_300">#FFE0E0E0</color> <!-- 默认灰色 -->
    <color name="white">#FFFFFFFF</color> <!-- 白色 -->
    <!--        -->

    <!-- Dialog and Button Colors -->
    <color name="dialog_border">#888888</color>
    <color name="primary_color">#2196F3</color>
    <color name="primary_dark_color">#1976D2</color>
    <color name="gray_text">#666666</color>

</resources>