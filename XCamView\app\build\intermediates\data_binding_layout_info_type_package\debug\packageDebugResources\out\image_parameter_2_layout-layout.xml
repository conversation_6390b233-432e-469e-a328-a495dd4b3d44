<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="image_parameter_2_layout" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\image_parameter_2_layout.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/image_parameter_2_layout_0" view="LinearLayout"><Expressions/><location startLine="0" startOffset="0" endLine="163" endOffset="14"/></Target><Target id="@+id/text_sharpness_value" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="42" endOffset="41"/></Target><Target id="@+id/btn_sharpness_reduce" view="ImageButton"><Expressions/><location startLine="51" startOffset="12" endLine="57" endOffset="47"/></Target><Target id="@+id/seekbar_sharpness_tv" view="SeekBar"><Expressions/><location startLine="59" startOffset="12" endLine="68" endOffset="57"/></Target><Target id="@+id/btn_sharpness_add" view="ImageButton"><Expressions/><location startLine="70" startOffset="12" endLine="76" endOffset="47"/></Target><Target id="@+id/text_denoise_value" view="TextView"><Expressions/><location startLine="100" startOffset="12" endLine="108" endOffset="41"/></Target><Target id="@+id/btn_denoise_reduce" view="ImageButton"><Expressions/><location startLine="117" startOffset="12" endLine="123" endOffset="47"/></Target><Target id="@+id/seekbar_denoise_tv" view="SeekBar"><Expressions/><location startLine="125" startOffset="12" endLine="134" endOffset="57"/></Target><Target id="@+id/btn_denoise_add" view="ImageButton"><Expressions/><location startLine="136" startOffset="12" endLine="142" endOffset="47"/></Target><Target id="@+id/btn_Default_image_parameter_2" view="Button"><Expressions/><location startLine="153" startOffset="8" endLine="158" endOffset="64"/></Target></Targets></Layout>