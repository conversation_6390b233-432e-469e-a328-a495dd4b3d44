// Generated by view binder compiler. Do not edit!
package com.touptek.xcamview.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.touptek.xcamview.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class BrowseGridLayoutBinding implements ViewBinding {
  @NonNull
  private final FrameLayout rootView;

  @NonNull
  public final ImageView gridCheckbox;

  @NonNull
  public final ImageView gridImage;

  @NonNull
  public final TextView gridLabel;

  @NonNull
  public final View selectedOverlay;

  @NonNull
  public final ImageView videoIndicator;

  private BrowseGridLayoutBinding(@NonNull FrameLayout rootView, @NonNull ImageView gridCheckbox,
      @NonNull ImageView gridImage, @NonNull TextView gridLabel, @NonNull View selectedOverlay,
      @NonNull ImageView videoIndicator) {
    this.rootView = rootView;
    this.gridCheckbox = gridCheckbox;
    this.gridImage = gridImage;
    this.gridLabel = gridLabel;
    this.selectedOverlay = selectedOverlay;
    this.videoIndicator = videoIndicator;
  }

  @Override
  @NonNull
  public FrameLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static BrowseGridLayoutBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static BrowseGridLayoutBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.browse_grid_layout, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static BrowseGridLayoutBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.grid_checkbox;
      ImageView gridCheckbox = ViewBindings.findChildViewById(rootView, id);
      if (gridCheckbox == null) {
        break missingId;
      }

      id = R.id.grid_image;
      ImageView gridImage = ViewBindings.findChildViewById(rootView, id);
      if (gridImage == null) {
        break missingId;
      }

      id = R.id.grid_label;
      TextView gridLabel = ViewBindings.findChildViewById(rootView, id);
      if (gridLabel == null) {
        break missingId;
      }

      id = R.id.selected_overlay;
      View selectedOverlay = ViewBindings.findChildViewById(rootView, id);
      if (selectedOverlay == null) {
        break missingId;
      }

      id = R.id.video_indicator;
      ImageView videoIndicator = ViewBindings.findChildViewById(rootView, id);
      if (videoIndicator == null) {
        break missingId;
      }

      return new BrowseGridLayoutBinding((FrameLayout) rootView, gridCheckbox, gridImage, gridLabel,
          selectedOverlay, videoIndicator);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
