<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (C) 2015 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
-->

<view
    xmlns:android="http://schemas.android.com/apk/res/android"
    class="com.google.android.material.snackbar.SnackbarContentLayout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:theme="@style/ThemeOverlay.AppCompat.Dark">

  <TextView
      android:id="@+id/snackbar_text"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_weight="1"
      android:layout_gravity="center_vertical|left|start"
      android:paddingTop="@dimen/design_snackbar_padding_vertical"
      android:paddingBottom="@dimen/design_snackbar_padding_vertical"
      android:paddingLeft="@dimen/design_snackbar_padding_horizontal"
      android:paddingRight="@dimen/design_snackbar_padding_horizontal"
      android:ellipsize="end"
      android:maxLines="@integer/design_snackbar_text_max_lines"
      android:textAlignment="viewStart"
      android:textAppearance="@style/TextAppearance.Design.Snackbar.Message"/>

  <Button
      android:id="@+id/snackbar_action"
      style="?attr/borderlessButtonStyle"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_marginStart="@dimen/design_snackbar_extra_spacing_horizontal"
      android:layout_marginLeft="@dimen/design_snackbar_extra_spacing_horizontal"
      android:layout_gravity="center_vertical|right|end"
      android:minWidth="48dp"
      android:textColor="?attr/colorAccent"
      android:visibility="gone"/>

</view>
