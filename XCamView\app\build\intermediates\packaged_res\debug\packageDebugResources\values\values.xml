<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="compression_levels">
        <item>无损</item>
        <item>高</item>
        <item>标准</item>
        <item>快速</item>
    </string-array>
    <string-array name="font_sizes">
        <item>小号字体</item>
        <item>中号字体</item>
        <item>大号字体</item>
    </string-array>
    <string-array name="label_styles">
        <item>标准标注</item>
        <item>带边框标注</item>
        <item>透明背景标注</item>
    </string-array>
    <string-array name="precision_options">
        <item>0位小数</item>
        <item>1位小数</item>
        <item>2位小数</item>
        <item>3位小数</item>
        <item>4位小数</item>
        <item>5位小数</item>
        <item>6位小数</item>
    </string-array>
    <string-array name="scene_options">
        <item>生物</item>
        <item>体视</item>
    </string-array>
    <string-array name="unit_options">
        <item>um (微米)</item>
        <item>mm (毫米)</item>
        <item>cm (厘米)</item>
        <item>m (米)</item>
        <item>inch (英寸)</item>
        <item>mil (密耳)</item>
    </string-array>
    <color name="Light_black">#FF2E2E2E</color>
    <color name="accent">#FF3B30</color>
    <color name="background">#F2F2F7</color>
    <color name="black">#FF000000</color>
    <color name="blue_500">#FF2196F3</color>
    <color name="border_color">#999999</color>
    <color name="colorISPBlue">#FF2196F3</color>
    <color name="colorISPText">#333333</color>
    <color name="colorPrimary">#FFA0A0A0</color>
    <color name="dialog_border">#888888</color>
    <color name="divider">#E5E5EA</color>
    <color name="gray_300">#FFE0E0E0</color>
    <color name="gray_icon_disabled">#B0B0B0</color>
    <color name="gray_text">#666666</color>
    <color name="gray_text_disabled">#A0A0A0</color>
    <color name="grey_background">#AAAAAA</color>
    <color name="on_surface_modern">#000000</color>
    <color name="popup_background_color">#555555</color>
    <color name="popup_text_color">#FFFFFF</color>
    <color name="primary">#007AFF</color>
    <color name="primary_color">#2196F3</color>
    <color name="primary_dark">#0056CC</color>
    <color name="primary_dark_color">#1976D2</color>
    <color name="primary_modern">#007AFF</color>
    <color name="selection_border">#007AFF</color>
    <color name="selection_overlay">#4D007AFF</color>
    <color name="solid_blue">#0000FF</color>
    <color name="surface">#FFFFFF</color>
    <color name="surface_secondary">#F8F8F8</color>
    <color name="text_primary">#000000</color>
    <color name="text_secondary">#8E8E93</color>
    <color name="text_tertiary">#C7C7CC</color>
    <color name="white">#FFFFFFFF</color>
    <color name="white_background">#FFFFFF</color>
    <dimen name="folder_item_width">300dp</dimen>
    <string name="about"/>
    <string name="app_name">XCamView</string>
    <string name="btn_add">+</string>
    <string name="btn_auto_exposure">Auto Exposure</string>
    <string name="btn_calc_luma_start">start luma</string>
    <string name="btn_calc_luma_stop">end luma</string>
    <string name="btn_exposure_compensation_colon">Exposure Compensation：</string>
    <string name="btn_exposure_gain_colon">Gain：</string>
    <string name="btn_exposure_time_colon">Exposure Time：</string>
    <string name="btn_lf_range_start">start assign range</string>
    <string name="btn_lf_range_stop">end assign range</string>
    <string name="btn_pq_start">start pq</string>
    <string name="btn_pq_stop">end pq</string>
    <string name="btn_record_start">start record</string>
    <string name="btn_reduce">-</string>
    <string name="button_desc_angle">角度测量</string>
    <string name="button_desc_angle_three">三点法画角度</string>
    <string name="button_desc_annulus">环形</string>
    <string name="button_desc_annulus2">双环</string>
    <string name="button_desc_arb_line">任意线</string>
    <string name="button_desc_arc">圆弧</string>
    <string name="button_desc_calibration">校准</string>
    <string name="button_desc_center_circle">中心圆</string>
    <string name="button_desc_delete_measurement">删除测量</string>
    <string name="button_desc_ellipse">椭圆</string>
    <string name="button_desc_export">导出</string>
    <string name="button_desc_five_ellipse">五点画椭圆</string>
    <string name="button_desc_horizon_line">水平线</string>
    <string name="button_desc_parallel_line">平行线</string>
    <string name="button_desc_point">点测量</string>
    <string name="button_desc_rectangle">矩形</string>
    <string name="button_desc_three_circle">三点画圆环</string>
    <string name="button_desc_three_line">三点画线测量</string>
    <string name="button_desc_three_rectangle">三点画矩形</string>
    <string name="button_desc_three_twocircles">三点画双圆</string>
    <string name="button_desc_three_vertical">三点画垂线</string>
    <string name="button_desc_twocircles">双圆</string>
    <string name="button_desc_vertical_line">垂直线</string>
    <string name="draw"/>
    <string name="folder"/>
    <string name="menu"/>
    <string name="menu_copy">复制到</string>
    <string name="menu_cut">移动到</string>
    <string name="menu_details">详细信息</string>
    <string name="menu_paste">粘贴</string>
    <string name="pause"/>
    <string name="record_video"/>
    <string name="screenshot_failed">screenshot failed</string>
    <string name="select_default_unit">选择默认单位</string>
    <string name="select_precision">选择测量精度</string>
    <string name="settings"/>
    <string name="take_photo"/>
    <string name="title_brightness_text">Brightness：</string>
    <string name="title_contrast_text">Contrast：</string>
    <string name="title_denoise_text">Denoise：</string>
    <string name="title_gamma_text">Gamma：</string>
    <string name="title_saturation_text">Saturation：</string>
    <string name="title_sharpness_text">Sharpness：</string>
    <string name="title_wb_blue_text">Blue：</string>
    <string name="title_wb_green_text">Green：</string>
    <string name="title_wb_red_text">Red：</string>
    <string name="zoom_in"/>
    <string name="zoom_out"/>
    <style name="FullScreenDialog" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
    </style>
    <style name="ModernActionButton">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_margin">16dp</item>
        <item name="android:textColor">@color/white_background</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="cornerRadius">8dp</item>
        <item name="backgroundTint">@color/gray_text_disabled</item>
    </style>
    <style name="ModernSettingTab">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:padding">16dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:background">@drawable/grey_background</item>
    </style>
    <declare-styleable name="RoundelMenu">
        <attr format="color|reference" name="round_menu_roundColor"/>
        <attr format="color|reference" name="round_menu_centerColor"/>
        <attr format="dimension" name="round_menu_expandedRadius"/>
        <attr format="dimension" name="round_menu_collapsedRadius"/>
        <attr format="integer" name="round_menu_duration"/>
        <attr format="integer" name="round_menu_item_anim_delay"/>
        <attr format="dimension" name="round_menu_item_width"/>
    </declare-styleable>
</resources>