1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.android.rockchip.mediacodecnew"
4    android:sharedUserId="android.uid.system"
5    android:versionCode="1"
6    android:versionName="1.0" >
7
8    <uses-sdk
9        android:minSdkVersion="31"
10        android:targetSdkVersion="35" />
11
12    <!-- 现有权限 -->
13    <uses-permission android:name="android.permission.CAMERA" />
13-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:7:5-65
13-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:7:22-62
14    <!-- 添加开机自启动权限 -->
15    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
15-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:9:5-81
15-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:9:22-78
16    <uses-permission android:name="android.permission.RECORD_AUDIO" />
16-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:11:5-71
16-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:11:22-68
17    <uses-permission
17-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:12:5-13:38
18        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
18-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:12:22-78
19        android:maxSdkVersion="32" />
19-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:13:9-35
20    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
20-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:14:5-79
20-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:14:22-77
21    <!-- 确保有以下权限 -->
22    <uses-permission android:name="android.permission.INTERNET" />
22-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:16:5-67
22-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:16:22-64
23    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
23-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:17:5-79
23-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:17:22-76
24
25    <!-- &lt;!&ndash; 屏幕录制相关权限 &ndash;&gt; -->
26    <!-- <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /> -->
27    <!-- <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> -->
28    <!-- <uses-permission android:name="android.permission.CAPTURE_VIDEO_OUTPUT" -->
29    <!-- tools:ignore="ProtectedPermissions" /> -->
30
31    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
31-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:25:5-75
31-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:25:22-73
32    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
32-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:26:5-75
32-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:26:22-73
33    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
33-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:27:5-78
33-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:27:22-76
34    <uses-permission android:name="android.permission.TETHER_PRIVILEGED" />
34-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:28:5-75
34-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:28:22-73
35    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
35-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:29:5-73
35-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:29:22-70
36    <uses-permission android:name="android.permission.MANAGE_WIFI_HOTSPOT" />
36-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:30:5-77
36-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:30:22-75
37    <uses-permission android:name="android.permission.CONNECTIVITY_INTERNAL" />
37-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:31:5-115
37-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:31:22-77
38    <uses-permission android:name="android.permission.START_TETHERING" />
38-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:32:5-109
38-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:32:22-71
39
40    <!-- TV模式所需权限 -->
41    <uses-permission android:name="com.android.providers.tv.permission.READ_EPG_DATA" />
41-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:35:5-89
41-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:35:22-86
42
43    <!-- 声明TV功能 -->
44    <uses-feature
44-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:38:5-86
45        android:name="android.software.live_tv"
45-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:38:19-58
46        android:required="false" />
46-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:38:59-83
47
48    <!-- 网络相关权限 -->
49    <uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES" />
49-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:41:5-78
49-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:41:22-75
50
51    <!-- 要求后置摄像头 -->
52    <uses-feature android:name="android.hardware.camera" />
52-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:44:5-60
52-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:44:19-57
53
54    <permission
54-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
55        android:name="com.android.rockchip.mediacodecnew.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
55-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
56        android:protectionLevel="signature" />
56-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
57
58    <uses-permission android:name="com.android.rockchip.mediacodecnew.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
58-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
58-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
59
60    <application
60-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:45:5-86:19
61        android:allowBackup="true"
61-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:46:9-35
62        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
62-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
63        android:dataExtractionRules="@xml/data_extraction_rules"
63-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:47:9-65
64        android:debuggable="true"
65        android:extractNativeLibs="false"
66        android:fullBackupContent="@xml/backup_rules"
66-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:48:9-54
67        android:icon="@mipmap/ic_launcher"
67-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:50:9-43
68        android:label="@string/app_name"
68-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:51:9-41
69        android:roundIcon="@mipmap/ic_launcher_round"
69-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:52:9-54
70        android:supportsRtl="true"
70-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:53:9-35
71        android:theme="@style/Theme.MediacodecNew" >
71-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:54:9-51
72
73        <!-- MainActivity 作为主启动Activity -->
74        <activity
74-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:57:9-65:20
75            android:name="com.android.rockchip.camera2.integrated.MainActivity"
75-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:58:13-80
76            android:exported="true" >
76-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:59:13-36
77            <intent-filter>
77-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:60:13-64:29
78                <action android:name="android.intent.action.MAIN" />
78-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:61:17-69
78-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:61:25-66
79
80                <category android:name="android.intent.category.LAUNCHER" />
80-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:63:17-77
80-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:63:27-74
81            </intent-filter>
82        </activity>
83        <!-- 添加 VideoDecoderActivity 的声明 -->
84        <activity android:name="com.android.rockchip.camera2.separated.VideoDecoderActivity" />
84-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:67:9-68:48
84-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:67:19-93
85        <!-- 添加 MediaBrowserActivity 的声明 -->
86        <activity android:name="com.android.rockchip.camera2.separated.MediaBrowserActivity" />
86-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:70:9-96
86-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:70:19-93
87        <!-- 添加 ImageViewerActivity 的声明 -->
88        <activity android:name="com.android.rockchip.camera2.separated.ImageViewerActivity" />
88-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:72:9-95
88-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:72:19-92
89
90        <!-- integrated版本的Activity声明 -->
91        <activity android:name="com.android.rockchip.camera2.integrated.browser.MediaBrowserActivity" />
91-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:75:9-105
91-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:75:19-102
92        <activity android:name="com.android.rockchip.camera2.integrated.browser.ImageViewerActivity" />
92-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:76:9-104
92-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:76:19-101
93        <activity android:name="com.android.rockchip.camera2.integrated.browser.TpVideoPlayerActivity" />
93-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:77:9-106
93-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:77:19-103
94        <activity android:name="com.android.rockchip.camera2.separated.TpVideoPlayerActivity" />
94-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:78:9-97
94-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:78:19-94
95
96        <service
96-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:80:9-85:44
97            android:name="com.touptek.video.internal.rtsp.service.RTSPService"
97-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:81:13-79
98            android:enabled="true"
98-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:82:13-35
99            android:exported="false"
99-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:83:13-37
100            android:foregroundServiceType="mediaProjection"
100-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:84:13-60
101            android:stopWithTask="false" />
101-->C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:85:13-41
102
103        <provider
103-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
104            android:name="androidx.startup.InitializationProvider"
104-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
105            android:authorities="com.android.rockchip.mediacodecnew.androidx-startup"
105-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
106            android:exported="false" >
106-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
107            <meta-data
107-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
108                android:name="androidx.emoji2.text.EmojiCompatInitializer"
108-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
109                android:value="androidx.startup" />
109-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
110            <meta-data
110-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b4ef1822637b01b35e1384fba3dd7b0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
111                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
111-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b4ef1822637b01b35e1384fba3dd7b0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
112                android:value="androidx.startup" />
112-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b4ef1822637b01b35e1384fba3dd7b0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
113            <meta-data
113-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
114                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
114-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
115                android:value="androidx.startup" />
115-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
116        </provider>
117
118        <receiver
118-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
119            android:name="androidx.profileinstaller.ProfileInstallReceiver"
119-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
120            android:directBootAware="false"
120-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
121            android:enabled="true"
121-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
122            android:exported="true"
122-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
123            android:permission="android.permission.DUMP" >
123-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
124            <intent-filter>
124-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
125                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
125-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
125-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
126            </intent-filter>
127            <intent-filter>
127-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
128                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
128-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
128-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
129            </intent-filter>
130            <intent-filter>
130-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
131                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
131-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
131-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
132            </intent-filter>
133            <intent-filter>
133-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
134                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
134-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
134-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
135            </intent-filter>
136        </receiver>
137    </application>
138
139</manifest>
