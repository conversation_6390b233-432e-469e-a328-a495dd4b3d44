package com.touptek.xcamview.activity.browse

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import com.touptek.xcamview.R
import com.touptek.video.internal.TpImageLoader
import java.io.File
import java.util.Locale
import android.view.HapticFeedbackConstants
import androidx.recyclerview.widget.RecyclerView
import kotlin.collections.forEach
import kotlin.collections.getOrElse
import kotlin.collections.isNotEmpty
import kotlin.collections.map
import kotlin.collections.sortedDescending
import kotlin.collections.toList
import kotlin.collections.toMutableList
import kotlin.io.extension
import kotlin.let
import kotlin.ranges.until
import kotlin.text.contains
import kotlin.text.toLowerCase

class TpThumbGridAdapter(
    private var imageFiles: List<File>,  // 改为可变文件列表
    private var labels: List<String>,    // 改为可变标签列表
    private val onClick: (Int) -> Unit,
    private val onDoubleClick: (Int) -> Unit,
    private val onUpdate: () -> Unit,
) : RecyclerView.Adapter<TpThumbGridAdapter.ViewHolder>() {

    private val selectedItems = mutableSetOf<Int>()
    var allowSelectionMode = true

    // 新增部分开始
    var isSelectionMode = false
    var onSelectionChanged: ((Int) -> Unit)? = null // 选中项变化回调
    // 新增部分结束

    inner class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val imageView: ImageView = view.findViewById(R.id.grid_image)
        val labelView: TextView = view.findViewById(R.id.grid_label)
        val selectedOverlay: View = view.findViewById(R.id.selected_overlay)
        val videoIndicator: ImageView = view.findViewById(R.id.video_indicator)

        val checkBox: ImageView = view.findViewById(R.id.grid_checkbox) // 勾选框

        var lastClickTime: Long = 0
        var pendingClickRunnable: Runnable? = null

    }

    fun updateData(newFiles: List<File>, newLabels: List<String>) {
        imageFiles = newFiles.toMutableList() //toMutableList将list改为一个可变集合
        imageFiles.forEach { file ->
        }

        labels = newLabels.toMutableList()



        if(imageFiles.isNotEmpty()) {
            notifyDataSetChanged()
            val previousSelected = selectedItems.toList()
            selectedItems.clear()
            previousSelected.forEach { notifyItemChanged(it) }
        }
        else
        {
            selectedItems.clear()  //切换文件夹时取消所有文件的选中
            clearData()
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.browse_grid_layout, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        //文件夹
        val item = imageFiles[position]

        // 如果是文件夹，显示文件夹图标
        if (item.isDirectory) {
            holder.imageView.setImageResource(R.drawable.ic_folder)
            holder.videoIndicator.visibility = View.GONE
            holder.labelView.text = item.name
        }
        // 如果是文件，正常加载缩略图
        else {
            // 使用TpImageLoader加载缩略图（用于列表显示）
            TpImageLoader.loadThumbnail(item.absolutePath, holder.imageView)

            // 添加视频标记
            val isVideo = item.name.contains(".mp4", ignoreCase = true) ||
                    getFileType(item) == "MP4"
//            holder.videoIndicator.visibility = if (isVideo) View.VISIBLE else View.GONE
            holder.videoIndicator.visibility = if (isVideo) View.VISIBLE else View.GONE
            holder.labelView.text = labels.getOrElse(position) { "" }
        }

        // 标签显示处理
        holder.labelView.text = labels.getOrElse(position) { "" }

        // 设置勾选框可见性和选中状态
        holder.checkBox.visibility = if (isSelectionMode) View.VISIBLE else View.INVISIBLE
        holder.checkBox.setImageResource(
            if (selectedItems.contains(position)) R.drawable.ic_checked
            else R.drawable.ic_unchecked
        )

        // 设置长按监听进入选择模式
        holder.itemView.setOnLongClickListener {
            if (allowSelectionMode) {
                if (!isSelectionMode) {
                    enterSelectionMode()
                    toggleSelection(position)
                    holder.itemView.performHapticFeedback(HapticFeedbackConstants.LONG_PRESS)
                    true
                } else {
                    exitSelectionMode()
                    false
                }
                true
            }else {
                false
            }
        }

        // 单击事件处理
        holder.itemView.setOnClickListener {
            if (isSelectionMode) {
                toggleSelection(position)
            } else {
                holder.pendingClickRunnable?.let { runnable ->
                    holder.itemView.removeCallbacks(runnable)
                    holder.pendingClickRunnable = null
                }

                val currentTime = System.currentTimeMillis()
                if (currentTime - holder.lastClickTime < 300) {
                    holder.lastClickTime = 0
                    onDoubleClick(position)
                } else {
                    holder.lastClickTime = currentTime
                    holder.pendingClickRunnable = Runnable {
                        onClick(position)
                        holder.pendingClickRunnable = null
                    }
                    holder.itemView.postDelayed(holder.pendingClickRunnable!!, 300)
                }
            }
        }
    }

    // 新增选择模式相关方法
    fun enterSelectionMode() {
        isSelectionMode = true
        notifyDataSetChanged() // 刷新所有视图显示勾选框
        onUpdate()
    }

    fun exitSelectionMode() {
        isSelectionMode = false
        selectedItems.clear()
        onSelectionChanged?.invoke(0) // 通知选中项清零
        notifyDataSetChanged() // 刷新所有视图隐藏勾选框
        onUpdate()
    }

    private fun toggleSelection(position: Int) {
        if (selectedItems.contains(position)) {
            selectedItems.remove(position)
        } else {
            selectedItems.add(position)
        }

        // 通知选中项数量变化
        onSelectionChanged?.invoke(selectedItems.size)

        notifyItemChanged(position)
    }

    override fun getItemCount(): Int {
        return imageFiles.size
    }

    fun isPositionSelected(position: Int): Boolean {
        return selectedItems.contains(position)
    }

    fun clearData() {
        imageFiles = emptyList()
        labels = emptyList()
        selectedItems.clear()
        // 直接通知整个数据集变化
        notifyDataSetChanged()
    }

    private fun getFileType(file: File): String {
        return when(file.extension.toLowerCase(Locale.US)) {
            "mp4", "mov", "avi" -> "MP4"
            "png" -> "PNG image"
            "jpg", "jpeg" -> "JPEG image"
            else -> "File"
        }
    }

    fun clearSelections() {
        // 获取当前选中的位置
        val selectedPositions = selectedItems.toList()
        selectedItems.clear()
        // 仅刷新之前选中的项
        selectedPositions.forEach { notifyItemChanged(it) }

        // 退出选择模式
        exitSelectionMode()
    }

    fun getFileAt(position: Int): File = imageFiles[position]
    val files: List<File> get() = imageFiles

    // 添加高度设置方法
    fun getSelectedFileNames(): List<String> {
        return selectedItems.map { position ->
            // 获取对应位置的文件对象
            imageFiles[position].name
        }.toList()
    }

    // 如果需要获取选中文件的完整路径或文件对象
    fun getSelectedFiles(): List<File> {
        return selectedItems.map { position ->
            imageFiles[position]
        }
    }

    // 检查是否有选中项
    fun hasSelectedItems() = selectedItems.isNotEmpty()

    // 获取选中项数量
    fun getSelectedCount() = selectedItems.size

    // 全选/取消全选
    fun toggleSelectAll(select: Boolean) {
//        if (select) {
//            repeat(itemCount) { position ->
//                selectedItems.add(position)
//            }
//        } else {
//            selectedItems.clear()
//        }
//        onSelectionChanged?.invoke(selectedItems.size)
//        notifyDataSetChanged()

        if (itemCount == 0) return
        if (select) {
            // 全选：添加所有位置到selectedItems
            for (position in 0 until itemCount) {
                selectedItems.add(position)
            }
        } else {
            // 取消全选
            selectedItems.clear()
        }

        // 确保在选择模式下
        if (select && !isSelectionMode) {
            isSelectionMode = true
        }

        // 更新选中数量回调
        onSelectionChanged?.invoke(selectedItems.size)

        // 刷新整个列表
        notifyDataSetChanged()

        // 通知Activity更新UI状态
        onUpdate()
    }

    fun deleteSelectedItems() {
        if (selectedItems.isEmpty()) return

        // 按降序排序（避免删除时索引变化）
        val sortedPositions = selectedItems.sortedDescending()

        // 创建可修改的数据副本
        val newImageFiles = imageFiles.toMutableList()
        val newLabels = labels.toMutableList()

        // 逐个删除选中项
        sortedPositions.forEach { position ->
            newImageFiles.removeAt(position)
            newLabels.removeAt(position)
        }

        // 更新数据源
        imageFiles = newImageFiles
        labels = newLabels

        // 清空选中状态
        selectedItems.clear()
        isSelectionMode = false

        // 通知数据变化
        notifyDataSetChanged()

        // 触发回调
        onSelectionChanged?.invoke(0)
        onUpdate()
    }
}