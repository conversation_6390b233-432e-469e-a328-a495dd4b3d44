<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="testdialog_settings" modulePackage="com.touptek.xcamview" filePath="app\src\main\res\layout\testdialog_settings.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.FrameLayout"><Targets><Target tag="layout/testdialog_settings_0" view="FrameLayout"><Expressions/><location startLine="0" startOffset="0" endLine="175" endOffset="13"/></Target><Target id="@+id/network_tab_container" view="LinearLayout"><Expressions/><location startLine="20" startOffset="12" endLine="41" endOffset="26"/></Target><Target id="@+id/item_network" view="TextView"><Expressions/><location startLine="32" startOffset="16" endLine="40" endOffset="45"/></Target><Target id="@+id/storage_tab_container" view="LinearLayout"><Expressions/><location startLine="43" startOffset="12" endLine="64" endOffset="26"/></Target><Target id="@+id/item_storage" view="TextView"><Expressions/><location startLine="55" startOffset="16" endLine="63" endOffset="45"/></Target><Target id="@+id/format_tab_container" view="LinearLayout"><Expressions/><location startLine="66" startOffset="12" endLine="87" endOffset="26"/></Target><Target id="@+id/item_format" view="TextView"><Expressions/><location startLine="78" startOffset="16" endLine="86" endOffset="45"/></Target><Target id="@+id/video_tab_container" view="LinearLayout"><Expressions/><location startLine="90" startOffset="12" endLine="111" endOffset="26"/></Target><Target id="@+id/item_video" view="TextView"><Expressions/><location startLine="102" startOffset="16" endLine="110" endOffset="45"/></Target><Target id="@+id/measurement_tab_container" view="LinearLayout"><Expressions/><location startLine="113" startOffset="12" endLine="134" endOffset="26"/></Target><Target id="@+id/item_measurement" view="TextView"><Expressions/><location startLine="125" startOffset="16" endLine="133" endOffset="45"/></Target><Target id="@+id/misc_tab_container" view="LinearLayout"><Expressions/><location startLine="137" startOffset="12" endLine="156" endOffset="26"/></Target><Target id="@+id/item_misc" view="TextView"><Expressions/><location startLine="149" startOffset="16" endLine="155" endOffset="45"/></Target><Target id="@+id/content_container" view="FrameLayout"><Expressions/><location startLine="160" startOffset="8" endLine="164" endOffset="57"/></Target><Target id="@+id/btn_close" view="ImageButton"><Expressions/><location startLine="167" startOffset="4" endLine="174" endOffset="55"/></Target></Targets></Layout>