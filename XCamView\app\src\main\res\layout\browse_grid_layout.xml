<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="4dp">

    <!-- 现代化卡片容器 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="12dp"
        app:cardElevation="2dp"
        android:layout_margin="2dp">

        <!-- 图片容器 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!-- 图片 -->
            <ImageView
                android:id="@+id/grid_image"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:adjustViewBounds="false"
                android:scaleType="centerCrop"
                android:background="@drawable/modern_image_background"/>

            <!-- 现代化选中覆盖层 -->
            <View
                android:id="@+id/selected_overlay"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/primary_modern"
                android:alpha="0.2"
                android:visibility="invisible"/>

            <!-- 视频指示器 -->
            <ImageView
                android:id="@+id/video_indicator"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_video_triangle"
                android:visibility="gone"/>

            <!-- 现代化选择框 -->
            <ImageView
                android:id="@+id/grid_checkbox"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_gravity="top|end"
                android:layout_marginEnd="8dp"
                android:layout_marginTop="8dp"
                android:visibility="invisible"
                android:src="@drawable/ic_unchecked"/>

        </FrameLayout>
    </androidx.cardview.widget.CardView>

    <!-- 现代化文本标签 -->
    <TextView
        android:id="@+id/grid_label"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:maxLines="2"
        android:ellipsize="end"
        android:padding="8dp"
        android:textColor="@color/on_surface_modern"
        android:textSize="12sp"
        android:lineSpacingExtra="2dp"/>

</LinearLayout>