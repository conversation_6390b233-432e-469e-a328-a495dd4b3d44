<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#000000">

    <!-- 顶部工具栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:orientation="horizontal"
        android:background="#1A1A1A"
        android:gravity="center_vertical"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <!-- 返回按钮 -->
        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_revert"
            android:contentDescription="返回"
            android:scaleType="center" />

        <!-- 标题 -->
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="三图对比"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp" />

        <!-- 位置切换按钮 -->
        <ImageButton
            android:id="@+id/btn_swap_position"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_swap_horiz"
            android:contentDescription="切换位置"
            android:scaleType="center"
            android:layout_marginEnd="8dp" />

        <!-- 同步模式按钮 -->
        <ImageButton
            android:id="@+id/btn_sync_mode"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_sync"
            android:contentDescription="同步模式"
            android:scaleType="center"
            android:layout_marginEnd="8dp" />

        <!-- 重置按钮 -->
        <ImageButton
            android:id="@+id/btn_reset"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_refresh"
            android:contentDescription="重置"
            android:scaleType="center" />

    </LinearLayout>

    <!-- 图片对比区域 - 横向1+1+1布局 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="horizontal">

        <!-- 左侧图片 -->
        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <com.touptek.ui.TpImageView
                android:id="@+id/image_left"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#000000" />

            <TextView
                android:id="@+id/tv_info_left"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|start"
                android:background="#CC000000"
                android:textColor="#FFFFFF"
                android:textSize="9sp"
                android:padding="4dp"
                android:text="图片1"
                android:maxLines="2"
                android:ellipsize="end" />

        </FrameLayout>

        <!-- 分隔线 -->
        <View
            android:layout_width="2dp"
            android:layout_height="match_parent"
            android:background="#333333" />

        <!-- 中间图片 -->
        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <com.touptek.ui.TpImageView
                android:id="@+id/image_center"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#000000" />

            <TextView
                android:id="@+id/tv_info_center"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|start"
                android:background="#CC000000"
                android:textColor="#FFFFFF"
                android:textSize="9sp"
                android:padding="4dp"
                android:text="图片2"
                android:maxLines="2"
                android:ellipsize="end" />

        </FrameLayout>

        <!-- 分隔线 -->
        <View
            android:layout_width="2dp"
            android:layout_height="match_parent"
            android:background="#333333" />

        <!-- 右侧图片 -->
        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1">

            <com.touptek.ui.TpImageView
                android:id="@+id/image_right"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#000000" />

            <TextView
                android:id="@+id/tv_info_right"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom|start"
                android:background="#CC000000"
                android:textColor="#FFFFFF"
                android:textSize="9sp"
                android:padding="4dp"
                android:text="图片3"
                android:maxLines="2"
                android:ellipsize="end" />

        </FrameLayout>

    </LinearLayout>

</LinearLayout>
