{"logs": [{"outputFile": "com.touptek.TouptekSDK-mergeReleaseResources-33:/values-v25/values-v25.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\516c1742b34e8dc510e9c7cea8adf175\\transformed\\appcompat-1.7.0\\res\\values-v25\\values-v25.xml", "from": {"startLines": "2,3,4,6", "startColumns": "4,4,4,4", "startOffsets": "55,126,209,308", "endLines": "2,3,5,7", "endColumns": "70,82,12,12", "endOffsets": "121,204,303,414"}}]}, {"outputFile": "com.touptek.TouptekSDK-release-35:/values-v25/values-v25.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\516c1742b34e8dc510e9c7cea8adf175\\transformed\\appcompat-1.7.0\\res\\values-v25\\values-v25.xml", "from": {"startLines": "2,3,4,6", "startColumns": "4,4,4,4", "startOffsets": "55,126,209,308", "endLines": "2,3,5,7", "endColumns": "70,82,12,12", "endOffsets": "121,204,303,414"}}]}]}