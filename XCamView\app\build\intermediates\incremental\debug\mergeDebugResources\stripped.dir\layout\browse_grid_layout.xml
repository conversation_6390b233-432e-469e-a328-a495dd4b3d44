<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="1dp">

    <!-- 主图片容器 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="180dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="1dp"
        app:cardUseCompatPadding="false">

        <ImageView
            android:id="@+id/grid_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:background="@color/surface_secondary"/>

        <!-- 选中状态覆盖层 -->
        <View
            android:id="@+id/selected_overlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/selection_overlay"
            android:visibility="gone"/>

        <!-- 视频指示器 -->
        <ImageView
            android:id="@+id/video_indicator"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center"
            android:src="@drawable/ic_play_modern"
            android:visibility="gone"/>

        <!-- 选择指示器 -->
        <ImageView
            android:id="@+id/grid_checkbox"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="top|end"
            android:layout_margin="8dp"
            android:src="@drawable/ic_check_circle"
            android:visibility="gone"/>

    </androidx.cardview.widget.CardView>

    <!-- 文件名标签（仅文件夹显示） -->
    <TextView
        android:id="@+id/grid_label"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@drawable/label_background"
        android:gravity="center"
        android:maxLines="1"
        android:ellipsize="end"
        android:padding="8dp"
        android:textColor="@color/text_primary"
        android:textSize="12sp"
        android:visibility="gone"/>

</FrameLayout>