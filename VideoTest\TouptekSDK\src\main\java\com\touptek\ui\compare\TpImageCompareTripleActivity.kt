package com.touptek.ui.compare

import android.content.Context
import android.content.Intent
import android.graphics.Matrix
import android.os.Bundle
import android.util.Log
import android.view.WindowManager
import android.widget.ImageButton
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.touptek.ui.TpImageView
import com.touptek.video.TpVideoSystem
import com.touptek.video.TpVideoConfig
import android.graphics.BitmapFactory
import java.io.File

import com.touptek.R;

/**
 * 三图对比Activity - 支持2+1布局的三张图片对比
 * 支持全局同步、分组同步、独立操作三种模式
 */
class TpImageCompareTripleActivity : AppCompatActivity() {
    
    companion object {
        private const val TAG = "TpImageCompareTriple"
        private const val EXTRA_IMAGE_PATHS = "extra_image_paths"

        /**
         * 启动三图对比Activity
         */
        @JvmStatic
        fun start(context: Context, imagePaths: List<String>) {
            if (imagePaths.size != 3) {
                Toast.makeText(context, "需要提供3张图片路径", Toast.LENGTH_SHORT).show()
                return
            }
            val intent = Intent(context, TpImageCompareTripleActivity::class.java).apply {
                putStringArrayListExtra(EXTRA_IMAGE_PATHS, ArrayList(imagePaths))
            }
            context.startActivity(intent)
        }
    }
    
    // UI组件
    private lateinit var btnBack: ImageButton
    private lateinit var btnSyncMode: ImageButton
    private lateinit var btnReset: ImageButton
    private lateinit var btnSwapPosition: ImageButton
    
    private lateinit var imageViewLeft: TpImageView
    private lateinit var imageViewCenter: TpImageView
    private lateinit var imageViewRight: TpImageView

    private lateinit var tvInfoLeft: TextView
    private lateinit var tvInfoCenter: TextView
    private lateinit var tvInfoRight: TextView
    
    // 数据
    private var imagePaths = mutableListOf<String>()
    private var currentSyncMode = TpImageSyncEngine.SyncMode.GLOBAL
    
    // 同步引擎
    private lateinit var syncEngine: TpImageSyncEngine
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupFullScreen()
        setContentView(R.layout.tp_image_compare_triple)

        initViews()
        getIntentData()
        setupSyncEngine()
        loadImages()
        updateImageInfo()
        setupClickListeners()
    }

    private fun setupFullScreen() {
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
    }
    
    private fun initViews() {
        btnBack = findViewById(R.id.btn_back)
        btnSyncMode = findViewById(R.id.btn_sync_mode)
        btnReset = findViewById(R.id.btn_reset)
        btnSwapPosition = findViewById(R.id.btn_swap_position)

        imageViewLeft = findViewById(R.id.image_left)
        imageViewCenter = findViewById(R.id.image_center)
        imageViewRight = findViewById(R.id.image_right)

        tvInfoLeft = findViewById(R.id.tv_info_left)
        tvInfoCenter = findViewById(R.id.tv_info_center)
        tvInfoRight = findViewById(R.id.tv_info_right)
    }
    
    private fun getIntentData() {
        imagePaths = intent.getStringArrayListExtra(EXTRA_IMAGE_PATHS)?.toMutableList() ?: mutableListOf()

        if (imagePaths.size != 3) {
            Toast.makeText(this, "图片路径无效", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        Log.d(TAG, "接收到${imagePaths.size}张图片路径，同步模式：$currentSyncMode")
    }
    
    private fun setupSyncEngine() {
        syncEngine = object : TpImageSyncEngine() {
            override fun getGroupTargets(sourceId: String): List<String> {
                // 三图对比的分组逻辑：横向1+1+1布局
                // GROUP模式下：左中为一组，右侧独立
                return when (currentSyncMode) {
                    TpImageSyncEngine.SyncMode.GROUP -> {
                        when (sourceId) {
                            "left", "center" -> listOf("left", "center").filter { it != sourceId }
                            else -> emptyList()
                        }
                    }
                    else -> emptyList()
                }
            }
        }

        syncEngine.apply {
            setSyncMode(currentSyncMode)
            setSyncEnabled(true)
            addImageView("left", imageViewLeft)
            addImageView("center", imageViewCenter)
            addImageView("right", imageViewRight)
        }
    }
    
    private fun loadImages() {
        val videoConfig = TpVideoConfig.createDefault4K()
        val videoSystem = TpVideoSystem(this, videoConfig)
        
        try {
            videoSystem.loadFullImage(imagePaths[0], imageViewLeft)
            videoSystem.loadFullImage(imagePaths[1], imageViewCenter)
            videoSystem.loadFullImage(imagePaths[2], imageViewRight)
            Log.d(TAG, "三张图片加载成功")
        } catch (e: Exception) {
            Log.e(TAG, "图片加载失败", e)
            Toast.makeText(this, "图片加载失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun updateImageInfo() {
        val imageViews = listOf(tvInfoLeft, tvInfoCenter, tvInfoRight)
        imagePaths.forEachIndexed { index, path ->
            val fileName = File(path).name
            val resolution = getImageResolution(path)
            val info = "$fileName ($resolution)"

            if (index < imageViews.size) {
                imageViews[index].text = info
            }
        }
    }
    
    private fun getImageResolution(imagePath: String): String {
        return try {
            val options = BitmapFactory.Options().apply {
                inJustDecodeBounds = true
            }
            BitmapFactory.decodeFile(imagePath, options)
            val width = options.outWidth
            val height = options.outHeight
            if (width > 0 && height > 0) {
                "${width}×${height}"
            } else {
                "未知分辨率"
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取图片分辨率失败: $imagePath", e)
            "未知分辨率"
        }
    }
    
    private fun setupClickListeners() {
        btnBack.setOnClickListener { finish() }
        
        btnSyncMode.setOnClickListener {
            cycleSyncMode()
        }
        
        btnReset.setOnClickListener {
            loadImages()
            Toast.makeText(this, "已重置图片位置", Toast.LENGTH_SHORT).show()
        }
        
        btnSwapPosition.setOnClickListener {
            swapImages()
        }
        
        updateSyncModeButtonState()
    }
    
    private fun cycleSyncMode() {
        currentSyncMode = when (currentSyncMode) {
            TpImageSyncEngine.SyncMode.GLOBAL -> TpImageSyncEngine.SyncMode.GROUP
            TpImageSyncEngine.SyncMode.GROUP -> TpImageSyncEngine.SyncMode.INDEPENDENT
            TpImageSyncEngine.SyncMode.INDEPENDENT -> TpImageSyncEngine.SyncMode.GLOBAL
        }
        
        syncEngine.setSyncMode(currentSyncMode)
        updateSyncModeButtonState()
        
        val modeText = when (currentSyncMode) {
            TpImageSyncEngine.SyncMode.GLOBAL -> "全局同步"
            TpImageSyncEngine.SyncMode.GROUP -> "分组同步"
            TpImageSyncEngine.SyncMode.INDEPENDENT -> "独立操作"
        }
        Toast.makeText(this, "切换到：$modeText", Toast.LENGTH_SHORT).show()
    }
    
    private fun updateSyncModeButtonState() {
        btnSyncMode.alpha = when (currentSyncMode) {
            TpImageSyncEngine.SyncMode.GLOBAL -> 1.0f
            TpImageSyncEngine.SyncMode.GROUP -> 0.7f
            TpImageSyncEngine.SyncMode.INDEPENDENT -> 0.4f
        }
    }
    
    private fun swapImages() {
        // 循环交换图片位置：1->2->3->1
        val tempPath = imagePaths[0]
        imagePaths[0] = imagePaths[1]
        imagePaths[1] = imagePaths[2]
        imagePaths[2] = tempPath
        
        // 重新加载图片
        loadImages()
        updateImageInfo()
        
        Toast.makeText(this, "已切换图片位置", Toast.LENGTH_SHORT).show()
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 清理同步引擎资源
        imageViewLeft.setMatrixChangeListener(null)
        imageViewCenter.setMatrixChangeListener(null)
        imageViewRight.setMatrixChangeListener(null)
    }
}
