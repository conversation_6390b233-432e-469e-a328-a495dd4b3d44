C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\color_red.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\color_tab_text_color.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable-v24_ic_launcher_foreground.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_about_d.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_about_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_add_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bg_rounded_dialog_light.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_border_box.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_bottom_panel_background.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_brow_d.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_browser_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_about_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_about_pressed.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_color_adjustment_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_color_adjustment_pressed.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_confirm_bg.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_draw_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_draw_pressed.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_exposure_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_exposure_pressed.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_flip_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_flip_pressed.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_folder_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_folder_pressed.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_image_processing_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_image_processing_pressed.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_menu_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_menu_pressed.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_pause_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_pause_pressed.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_power_frequency_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_power_frequency_pressed.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_record_video_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_record_video_pressed.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_rounded_default.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_settings_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_settings_pressed.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_take_photo_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_take_photo_pressed.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_white_balance_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_white_balance_pressed.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_zoom_in_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_zoom_in_pressed.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_zoom_out_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_btn_zoom_out_pressed.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_button_background.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_button_border_selected.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_config_d.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_config_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_delete_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_dialog_background.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_dialog_border.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_divider.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_divider_vertical_light.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_exposure_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_flip_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_floating_nav_button_bg.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_floating_nav_button_bg_enhanced.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_floating_panel_bg.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_freeze_d.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_freeze_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_grey_background.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_groupbox_border.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_groupbox_title_background.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_home_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_hz_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_about.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_action1.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_action2.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_action3.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_action4.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_action5.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_add_modern.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_allselect.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_angle_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_annotation_arrow_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_annulus2_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_annulus_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_arbline_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_arc_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_arrow_back.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_arrowleft.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_arrowright_d.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_calibration_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_cancel.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_cancel_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_centerc_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_checked.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_close.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_close_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_color_adjust.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_color_adjustment.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_compare.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_config_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_copy.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_cut.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_delete_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_details.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_draw.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_ellipse_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_export_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_exposure.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_fiveellipse_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_flip.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_fold.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_fold_pressed.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_folder.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_folder_modern.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_fourptangle_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_hline_d.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_hline_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_home_modern.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_image_processing.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_launcher_background.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_lock_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_menu.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_nav_back.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_nav_back_classic.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_nav_back_curved.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_nav_back_large.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_nav_back_option2.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_nav_back_option3.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_nav_back_shadow.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_nav_back_simple.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_nav_next.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_nav_next_large.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_nav_next_shadow.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_nav_previous.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_nav_previous_large.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_nav_previous_shadow.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_parallel_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_paste.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_pause.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_pic.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_picture.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_point_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_polygon_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_power_frequency.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_preview.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_randomcurve_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_record_start.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_record_video.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_rectangle_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_return.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_scale_bar_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_select_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_settings.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_storage.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_take_photo.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_text_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_threecircle_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_threeline_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_threepttwowircles_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_threerectangle_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_threevertical_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_unchecked.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_video.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_video_triangle.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_vline_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_wb_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_white.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_white_balance.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_zoom_in.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_zoom_out.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_image_border_normal.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_imageprocess_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_isp_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_measure_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_modern_image_background.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_nav_separator.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_next_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_oval_button_background.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_popup_background.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_record_start_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_rounded_border.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_rounded_button_background.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_scenechange_d.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_scenechange_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_selector_settings_tab.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_selector_tab_background.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_snap_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_status_banner_bg.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_stepframe_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_sub_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tab_selected_bg.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_thumb_blue.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_thumb_blue_selector.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_title_background.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tp_custom_enabled_selector.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tp_custom_radionbutton.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tp_custom_seekbar.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tp_custom_seekbar_thumb.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tp_switch_thumb.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tp_switch_track.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_track_blue_selector.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_zoomin_d.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_zoomin_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_zoomout_d.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_zoomout_n.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\font_kai.ttf.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\font_song.ttf.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_main.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_touptek.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_touptek_btn.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_welcome.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_autoae_layout.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_browse_grid_layout.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_browse_layout.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_copydialog_settings.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_file_details.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_modern_settings.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_settings.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_flip_layout.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_folder_item.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_format_settings.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_measurement_settings.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_network_settings.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_storage_settings.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_hz_layout.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_image_parameter_2_layout.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_image_parameter_layout.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_image_viewer.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_layout_input_info_item.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_measurement_layout.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_operation_grid_layout.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_popup_config_menu.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_popup_menu_layout.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_right_panel_layout.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_scene_layout.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_settings_misc.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_settings_record.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_testdialog_settings.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_video_layout.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_videodecode_layout.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_whitebalance_layout.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-anydpi-v26_ic_launcher.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-anydpi-v26_ic_launcher_round.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-hdpi_ic_launcher.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-hdpi_ic_launcher_round.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-mdpi_ic_launcher.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-mdpi_ic_launcher_round.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xhdpi_ic_launcher.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xhdpi_ic_launcher_round.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xxhdpi_ic_launcher.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xxhdpi_ic_launcher_round.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xxxhdpi_ic_launcher.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\mipmap-xxxhdpi_ic_launcher_round.png.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-af_values-af.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-am_values-am.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ar_values-ar.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-as_values-as.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-az_values-az.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-b+es+419_values-b+es+419.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-b+sr+Latn_values-b+sr+Latn.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-be_values-be.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-bg_values-bg.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-bn_values-bn.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-bs_values-bs.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ca_values-ca.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-cs_values-cs.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-da_values-da.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-de_values-de.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-el_values-el.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rAU_values-en-rAU.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rCA_values-en-rCA.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rGB_values-en-rGB.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rIN_values-en-rIN.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rXC_values-en-rXC.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-es-rUS_values-es-rUS.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-es_values-es.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-et_values-et.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-eu_values-eu.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-fa_values-fa.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-fi_values-fi.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-fr-rCA_values-fr-rCA.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-fr_values-fr.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-gl_values-gl.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-gu_values-gu.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h320dp-port-v13_values-h320dp-port-v13.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h360dp-land-v13_values-h360dp-land-v13.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h480dp-land-v13_values-h480dp-land-v13.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h550dp-port-v13_values-h550dp-port-v13.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h720dp-v13_values-h720dp-v13.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hdpi-v4_values-hdpi-v4.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hi_values-hi.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hr_values-hr.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hu_values-hu.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hy_values-hy.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-in_values-in.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-is_values-is.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-it_values-it.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-iw_values-iw.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ja_values-ja.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ka_values-ka.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-kk_values-kk.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-km_values-km.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-kn_values-kn.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ko_values-ko.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ky_values-ky.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-land_values-land.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-large-v4_values-large-v4.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ldltr-v21_values-ldltr-v21.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ldrtl-v17_values-ldrtl-v17.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-lo_values-lo.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-lt_values-lt.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-lv_values-lv.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-mk_values-mk.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ml_values-ml.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-mn_values-mn.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-mr_values-mr.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ms_values-ms.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-my_values-my.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-nb_values-nb.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ne_values-ne.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-night-v8_values-night-v8.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-nl_values-nl.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-or_values-or.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pa_values-pa.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pl_values-pl.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-port_values-port.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pt-rBR_values-pt-rBR.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pt-rPT_values-pt-rPT.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pt_values-pt.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ro_values-ro.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ru_values-ru.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-si_values-si.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sk_values-sk.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sl_values-sl.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-small-v4_values-small-v4.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sq_values-sq.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sr_values-sr.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sv_values-sv.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sw600dp-v13_values-sw600dp-v13.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sw_values-sw.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ta_values-ta.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-te_values-te.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-th_values-th.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-tl_values-tl.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-tr_values-tr.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-uk_values-uk.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ur_values-ur.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-uz_values-uz.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v16_values-v16.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v17_values-v17.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v18_values-v18.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v21_values-v21.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v22_values-v22.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v23_values-v23.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v24_values-v24.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v25_values-v25.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v26_values-v26.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v28_values-v28.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v31_values-v31.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-vi_values-vi.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-w320dp-land-v13_values-w320dp-land-v13.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-w360dp-port-v13_values-w360dp-port-v13.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-w400dp-port-v13_values-w400dp-port-v13.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-w600dp-land-v13_values-w600dp-land-v13.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-watch-v20_values-watch-v20.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-watch-v21_values-watch-v21.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-xlarge-v4_values-xlarge-v4.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-zh-rCN_values-zh-rCN.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-zh-rHK_values-zh-rHK.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-zh-rTW_values-zh-rTW.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values-zu_values-zu.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\values_values.arsc.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\xml_backup_rules.xml.flat C:\hhx\rk3588\AndroidStudio\XCamView\app\build\intermediates\merged_res\debug\mergeDebugResources\xml_data_extraction_rules.xml.flat 