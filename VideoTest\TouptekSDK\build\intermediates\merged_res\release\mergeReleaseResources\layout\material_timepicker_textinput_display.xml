<?xml version="1.0" encoding="utf-8"?><!--
     Copyright (C) 2020 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_width="wrap_content"
  android:layout_height="wrap_content"
  android:paddingTop="8dp"
  android:layoutDirection="ltr"
  tools:showIn="@layout/material_textinput_timepicker">

  <include
    android:id="@+id/material_hour_text_input"
    layout="@layout/material_chip_input_combo" />

  <include layout="@layout/material_clock_display_divider" />

  <include
    android:id="@+id/material_minute_text_input"
    layout="@layout/material_chip_input_combo" />

</LinearLayout>
