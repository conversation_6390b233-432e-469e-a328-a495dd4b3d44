-- Merging decision tree log ---
manifest
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:2:1-89:12
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:2:1-89:12
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:2:1-89:12
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:2:1-89:12
MERGED from [TP2HD-VisionSDK.aar] C:\Users\<USER>\.gradle\caches\transforms-4\31ff28962214739ec5f210942a37f7b0\transformed\TP2HD-VisionSDK\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\daa8b0903db4f3f55b59ed55bf8e8901\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\87d4b0e5f59c50d8b9685d5d18e78c3f\transformed\constraintlayout-2.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\0dd63681b18b7257eb691e5014540e53\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\516c1742b34e8dc510e9c7cea8adf175\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a718476edbcd3152118a2456cad3313\transformed\glide-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1d55179b15f9424c1dbe7c8001c54e27\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\d51940efaebc98a75d0b070aeb22e9db\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\d303a11a13a23dc9f50a93bf47e8e414\transformed\activity-1.10.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.pedroSG94:RTSP-Server:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\0cccaab1906b831bd71de093a4d59948\transformed\RTSP-Server-1.2.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.pedroSG94.RootEncoder:library:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\c3e0c2fc25e229c82b7cd6402fd24258\transformed\library-2.3.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\29b3fb0e8205a3260f019fea680594e3\transformed\recyclerview-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3fc99525ac28f03d0b1f80a04a8db13a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ea2025fbd826a14503d698dff7ab285\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\f560c1a526c64bfbeb0fd0eb4fd0ede9\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\d70e4ec4137b67f25d65dfb80367a93e\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\247a81cab4a84f661d387ccc9827036b\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\df5f0913956057ce74df6ea0e285d477\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fbbeeb72698ceb8efab4738610e9828d\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\4a3cb0fab2ec4da466d2a27e96b8d559\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\3d10b3b0cfce9020ee537e4b78c6b027\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b4ef1822637b01b35e1384fba3dd7b0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\bbb5f3a202add9afb3a3f33e8c864304\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a590efca7681b4ca8db2829e65018a82\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\72108e2f128e373cf1024cab12411eb4\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\4ea8be37b63de092f7840b8d869587c9\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8d384c571a0b2c80264ecdd670a33b7c\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\635c55fa60992e4a7319acab4786ddf9\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\8693bdf28895134513adfc38cff5f331\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\eda911f91de9df4012a91c73960b3886\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\028216cd99e2286b5fa84bd0f8065759\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ad02253041b25119b63061d5f9335a71\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [com.github.pedroSG94.RootEncoder:encoder:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\d89cbfcddfbf60b140729dabff3262ce\transformed\encoder-2.3.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.pedroSG94.RootEncoder:rtmp:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\88c59366069c2be4940781c38c8f8c97\transformed\rtmp-2.3.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.pedroSG94.RootEncoder:rtsp:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\a1806c6b007693e829ac909fad63d2a5\transformed\rtsp-2.3.5\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.pedroSG94.RootEncoder:srt:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\bd675622cf151f61c72f44a026529ac2\transformed\srt-2.3.5\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.pedroSG94.RootEncoder:common:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\e88190595a81ec32a4f8857870423b01\transformed\common-2.3.5\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\b4e80d0a618703e32c8c56c24e6f01ef\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d74ef11897c228f65a8aec79187919a\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f5c5299451eade7450fc4234389a314\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\1d665b7bfbe53d0d667d960b497e88bf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bf4ec10d154be3b8464a65883c8593a0\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\b8d979fc8e2b79eeaa8fbe8d787801c8\transformed\gifdecoder-4.15.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-4\1b053540c3eebf64ad96dd20fd2c9fd9\transformed\exifinterface-1.3.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ee9579ffeea0c6ea180faa153cdaf3c9\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f0b2ca8939f6ef718808e16d4856c1b0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\41334a0bbb7d6621af698bd550fc5d40\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\936f03ccb77fff06e577084a82286a8a\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb87e150ab3800e8cef58cf6fcad9485\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bf7ebbd33f8a540548477e65860c6935\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [io.github.beyka:Android-TiffBitmapFactory:*******] C:\Users\<USER>\.gradle\caches\transforms-4\05ccd984ab7d408fa20eeb33412426ea\transformed\Android-TiffBitmapFactory-*******\AndroidManifest.xml:2:1-14:12
	package
		INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
	android:sharedUserId
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:3:5-46
	android:versionName
		INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:4:5-51
	android:versionCode
		INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:7:5-65
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:7:22-62
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:9:5-81
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:11:5-71
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:11:22-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:12:5-13:38
	android:maxSdkVersion
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:13:9-35
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:12:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:14:5-79
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:14:22-77
uses-permission#android.permission.INTERNET
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:16:5-67
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:16:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:17:5-79
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:17:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:25:5-75
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:25:22-73
uses-permission#android.permission.CHANGE_WIFI_STATE
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:26:5-75
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:26:22-73
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:27:5-78
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:27:22-76
uses-permission#android.permission.TETHER_PRIVILEGED
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:28:5-75
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:28:22-73
uses-permission#android.permission.WRITE_SETTINGS
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:29:5-73
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:29:22-70
uses-permission#android.permission.MANAGE_WIFI_HOTSPOT
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:30:5-77
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:30:22-75
uses-permission#android.permission.CONNECTIVITY_INTERNAL
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:31:5-115
	tools:ignore
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:31:78-113
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:31:22-77
uses-permission#android.permission.START_TETHERING
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:32:5-109
	tools:ignore
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:32:72-107
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:32:22-71
uses-permission#com.android.providers.tv.permission.READ_EPG_DATA
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:35:5-89
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:35:22-86
uses-feature#android.software.live_tv
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:38:5-86
	android:required
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:38:59-83
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:38:19-58
uses-permission#android.permission.NEARBY_WIFI_DEVICES
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:41:5-78
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:41:22-75
uses-feature#android.hardware.camera
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:44:5-60
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:44:19-57
application
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:45:5-86:19
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:45:5-86:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\daa8b0903db4f3f55b59ed55bf8e8901\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\daa8b0903db4f3f55b59ed55bf8e8901\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\87d4b0e5f59c50d8b9685d5d18e78c3f\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\87d4b0e5f59c50d8b9685d5d18e78c3f\transformed\constraintlayout-2.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a718476edbcd3152118a2456cad3313\transformed\glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a718476edbcd3152118a2456cad3313\transformed\glide-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b4ef1822637b01b35e1384fba3dd7b0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b4ef1822637b01b35e1384fba3dd7b0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\1d665b7bfbe53d0d667d960b497e88bf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\1d665b7bfbe53d0d667d960b497e88bf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\b8d979fc8e2b79eeaa8fbe8d787801c8\transformed\gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\b8d979fc8e2b79eeaa8fbe8d787801c8\transformed\gifdecoder-4.15.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f0b2ca8939f6ef718808e16d4856c1b0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f0b2ca8939f6ef718808e16d4856c1b0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [io.github.beyka:Android-TiffBitmapFactory:*******] C:\Users\<USER>\.gradle\caches\transforms-4\05ccd984ab7d408fa20eeb33412426ea\transformed\Android-TiffBitmapFactory-*******\AndroidManifest.xml:9:5-12:19
MERGED from [io.github.beyka:Android-TiffBitmapFactory:*******] C:\Users\<USER>\.gradle\caches\transforms-4\05ccd984ab7d408fa20eeb33412426ea\transformed\Android-TiffBitmapFactory-*******\AndroidManifest.xml:9:5-12:19
	android:extractNativeLibs
		INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:53:9-35
	android:label
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:51:9-41
	android:fullBackupContent
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:48:9-54
	android:roundIcon
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:52:9-54
	tools:targetApi
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:55:9-29
	android:icon
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:50:9-43
	android:allowBackup
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:46:9-35
		REJECTED from [io.github.beyka:Android-TiffBitmapFactory:*******] C:\Users\<USER>\.gradle\caches\transforms-4\05ccd984ab7d408fa20eeb33412426ea\transformed\Android-TiffBitmapFactory-*******\AndroidManifest.xml:10:9-36
	android:theme
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:54:9-51
	android:dataExtractionRules
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:47:9-65
	tools:replace
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:49:9-44
activity#com.android.rockchip.camera2.integrated.MainActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:57:9-65:20
	android:exported
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:59:13-36
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:58:13-80
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:60:13-64:29
action#android.intent.action.MAIN
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:61:17-69
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:61:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:63:17-77
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:63:27-74
activity#com.android.rockchip.camera2.separated.VideoDecoderActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:67:9-68:48
	tools:ignore
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:68:13-45
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:67:19-93
activity#com.android.rockchip.camera2.separated.MediaBrowserActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:70:9-96
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:70:19-93
activity#com.android.rockchip.camera2.separated.ImageViewerActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:72:9-95
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:72:19-92
activity#com.android.rockchip.camera2.integrated.browser.MediaBrowserActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:75:9-105
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:75:19-102
activity#com.android.rockchip.camera2.integrated.browser.ImageViewerActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:76:9-104
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:76:19-101
activity#com.android.rockchip.camera2.integrated.browser.TpVideoPlayerActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:77:9-106
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:77:19-103
activity#com.android.rockchip.camera2.separated.TpVideoPlayerActivity
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:78:9-97
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:78:19-94
service#com.touptek.video.internal.rtsp.service.RTSPService
ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:80:9-85:44
	android:enabled
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:82:13-35
	android:exported
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:83:13-37
	android:stopWithTask
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:85:13-41
	android:foregroundServiceType
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:84:13-60
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml:81:13-79
uses-sdk
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
MERGED from [TP2HD-VisionSDK.aar] C:\Users\<USER>\.gradle\caches\transforms-4\31ff28962214739ec5f210942a37f7b0\transformed\TP2HD-VisionSDK\AndroidManifest.xml:5:5-44
MERGED from [TP2HD-VisionSDK.aar] C:\Users\<USER>\.gradle\caches\transforms-4\31ff28962214739ec5f210942a37f7b0\transformed\TP2HD-VisionSDK\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\daa8b0903db4f3f55b59ed55bf8e8901\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\daa8b0903db4f3f55b59ed55bf8e8901\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\87d4b0e5f59c50d8b9685d5d18e78c3f\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\87d4b0e5f59c50d8b9685d5d18e78c3f\transformed\constraintlayout-2.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\0dd63681b18b7257eb691e5014540e53\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\0dd63681b18b7257eb691e5014540e53\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\516c1742b34e8dc510e9c7cea8adf175\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\516c1742b34e8dc510e9c7cea8adf175\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a718476edbcd3152118a2456cad3313\transformed\glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\9a718476edbcd3152118a2456cad3313\transformed\glide-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1d55179b15f9424c1dbe7c8001c54e27\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1d55179b15f9424c1dbe7c8001c54e27\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\d51940efaebc98a75d0b070aeb22e9db\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\d51940efaebc98a75d0b070aeb22e9db\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\d303a11a13a23dc9f50a93bf47e8e414\transformed\activity-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\d303a11a13a23dc9f50a93bf47e8e414\transformed\activity-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.pedroSG94:RTSP-Server:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\0cccaab1906b831bd71de093a4d59948\transformed\RTSP-Server-1.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94:RTSP-Server:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\0cccaab1906b831bd71de093a4d59948\transformed\RTSP-Server-1.2.1\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:library:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\c3e0c2fc25e229c82b7cd6402fd24258\transformed\library-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:library:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\c3e0c2fc25e229c82b7cd6402fd24258\transformed\library-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\29b3fb0e8205a3260f019fea680594e3\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\29b3fb0e8205a3260f019fea680594e3\transformed\recyclerview-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3fc99525ac28f03d0b1f80a04a8db13a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3fc99525ac28f03d0b1f80a04a8db13a\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ea2025fbd826a14503d698dff7ab285\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ea2025fbd826a14503d698dff7ab285\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\f560c1a526c64bfbeb0fd0eb4fd0ede9\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\f560c1a526c64bfbeb0fd0eb4fd0ede9\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\d70e4ec4137b67f25d65dfb80367a93e\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\d70e4ec4137b67f25d65dfb80367a93e\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\247a81cab4a84f661d387ccc9827036b\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\247a81cab4a84f661d387ccc9827036b\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\df5f0913956057ce74df6ea0e285d477\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\df5f0913956057ce74df6ea0e285d477\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fbbeeb72698ceb8efab4738610e9828d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\fbbeeb72698ceb8efab4738610e9828d\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\4a3cb0fab2ec4da466d2a27e96b8d559\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\4a3cb0fab2ec4da466d2a27e96b8d559\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\3d10b3b0cfce9020ee537e4b78c6b027\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\3d10b3b0cfce9020ee537e4b78c6b027\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b4ef1822637b01b35e1384fba3dd7b0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b4ef1822637b01b35e1384fba3dd7b0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\bbb5f3a202add9afb3a3f33e8c864304\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\bbb5f3a202add9afb3a3f33e8c864304\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a590efca7681b4ca8db2829e65018a82\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\a590efca7681b4ca8db2829e65018a82\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\72108e2f128e373cf1024cab12411eb4\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\72108e2f128e373cf1024cab12411eb4\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\4ea8be37b63de092f7840b8d869587c9\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\4ea8be37b63de092f7840b8d869587c9\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8d384c571a0b2c80264ecdd670a33b7c\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\8d384c571a0b2c80264ecdd670a33b7c\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\635c55fa60992e4a7319acab4786ddf9\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\635c55fa60992e4a7319acab4786ddf9\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\8693bdf28895134513adfc38cff5f331\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\8693bdf28895134513adfc38cff5f331\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\eda911f91de9df4012a91c73960b3886\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\eda911f91de9df4012a91c73960b3886\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\028216cd99e2286b5fa84bd0f8065759\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\028216cd99e2286b5fa84bd0f8065759\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ad02253041b25119b63061d5f9335a71\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\ad02253041b25119b63061d5f9335a71\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.pedroSG94.RootEncoder:encoder:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\d89cbfcddfbf60b140729dabff3262ce\transformed\encoder-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:encoder:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\d89cbfcddfbf60b140729dabff3262ce\transformed\encoder-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:rtmp:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\88c59366069c2be4940781c38c8f8c97\transformed\rtmp-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:rtmp:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\88c59366069c2be4940781c38c8f8c97\transformed\rtmp-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:rtsp:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\a1806c6b007693e829ac909fad63d2a5\transformed\rtsp-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:rtsp:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\a1806c6b007693e829ac909fad63d2a5\transformed\rtsp-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:srt:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\bd675622cf151f61c72f44a026529ac2\transformed\srt-2.3.5\AndroidManifest.xml:20:5-44
MERGED from [com.github.pedroSG94.RootEncoder:srt:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\bd675622cf151f61c72f44a026529ac2\transformed\srt-2.3.5\AndroidManifest.xml:20:5-44
MERGED from [com.github.pedroSG94.RootEncoder:common:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\e88190595a81ec32a4f8857870423b01\transformed\common-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [com.github.pedroSG94.RootEncoder:common:2.3.5] C:\Users\<USER>\.gradle\caches\transforms-4\e88190595a81ec32a4f8857870423b01\transformed\common-2.3.5\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\b4e80d0a618703e32c8c56c24e6f01ef\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\b4e80d0a618703e32c8c56c24e6f01ef\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d74ef11897c228f65a8aec79187919a\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\2d74ef11897c228f65a8aec79187919a\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f5c5299451eade7450fc4234389a314\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\3f5c5299451eade7450fc4234389a314\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\1d665b7bfbe53d0d667d960b497e88bf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\1d665b7bfbe53d0d667d960b497e88bf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bf4ec10d154be3b8464a65883c8593a0\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bf4ec10d154be3b8464a65883c8593a0\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\b8d979fc8e2b79eeaa8fbe8d787801c8\transformed\gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.15.1] C:\Users\<USER>\.gradle\caches\transforms-4\b8d979fc8e2b79eeaa8fbe8d787801c8\transformed\gifdecoder-4.15.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-4\1b053540c3eebf64ad96dd20fd2c9fd9\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-4\1b053540c3eebf64ad96dd20fd2c9fd9\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ee9579ffeea0c6ea180faa153cdaf3c9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ee9579ffeea0c6ea180faa153cdaf3c9\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f0b2ca8939f6ef718808e16d4856c1b0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\f0b2ca8939f6ef718808e16d4856c1b0\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\41334a0bbb7d6621af698bd550fc5d40\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\41334a0bbb7d6621af698bd550fc5d40\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\936f03ccb77fff06e577084a82286a8a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\936f03ccb77fff06e577084a82286a8a\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb87e150ab3800e8cef58cf6fcad9485\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb87e150ab3800e8cef58cf6fcad9485\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bf7ebbd33f8a540548477e65860c6935\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bf7ebbd33f8a540548477e65860c6935\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [io.github.beyka:Android-TiffBitmapFactory:*******] C:\Users\<USER>\.gradle\caches\transforms-4\05ccd984ab7d408fa20eeb33412426ea\transformed\Android-TiffBitmapFactory-*******\AndroidManifest.xml:5:5-7:41
MERGED from [io.github.beyka:Android-TiffBitmapFactory:*******] C:\Users\<USER>\.gradle\caches\transforms-4\05ccd984ab7d408fa20eeb33412426ea\transformed\Android-TiffBitmapFactory-*******\AndroidManifest.xml:5:5-7:41
	android:targetSdkVersion
		INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\hhx\rk3588\AndroidStudio\VideoTest\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b4ef1822637b01b35e1384fba3dd7b0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b4ef1822637b01b35e1384fba3dd7b0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\1d665b7bfbe53d0d667d960b497e88bf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\1d665b7bfbe53d0d667d960b497e88bf\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\ddf5422c8b6e55349d7af9e794a618a0\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b4ef1822637b01b35e1384fba3dd7b0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b4ef1822637b01b35e1384fba3dd7b0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\8b4ef1822637b01b35e1384fba3dd7b0\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.android.rockchip.mediacodecnew.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.android.rockchip.mediacodecnew.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\eb1b2774fc2a097a3b4499132d439d2f\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\6bb683487dc0e259c4339138e324f276\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
